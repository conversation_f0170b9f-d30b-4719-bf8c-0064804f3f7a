{"_comments": {"description": "挂链工具集配置文件 - Link Tool Suite Configuration", "version": "2.0", "last_updated": "2024-12-05", "note": "请根据实际情况修改以下配置项"}, "excel": {"_comments": "原始关键词规则表配置 - 用于验证的源文件", "file_path": "F:/蕾奥工作/11.自研挂链/v2.0/低空4.xlsx", "sheet_name": "原版"}, "keyword_columns": ["like_keyword", "must_keyword", "unlike_keyword"], "column_mappings": {"like_keyword_column": "like_keyword", "must_keyword_column": "must_keyword", "unlike_keyword_column": "unlike_keyword"}, "validation_rules": {"_comments": "关键词规则验证配置", "max_errors_display": 10, "highlight_color": "FFFF00"}, "pattern_conversion": {"_comments": "关键词模式转换配置 - 将验证通过的规则转换为正则表达式格式", "max_chars": 200, "source_file_path": "auto_detect", "output_file_path": "converted_keyword_rules.xlsx", "output_sheet_name": "转换后规则", "column_prefix": "converted", "enable_logging": true}, "keyword_matching": {"_comments": "关键词智能匹配配置 - choice=3功能配置", "keyword_file_path": "auto_detect", "keyword_sheet_name": "转换后规则", "input_file_path": "", "input_sheet_name": "Sheet1", "output_file_path": "matching_results.csv", "enable_performance_optimization": true, "batch_size": 50, "max_workers": 8, "parallel_mode": "process", "enable_logging": true, "text_processing": {"_comments": "文本处理相关配置", "enable_text_length_precheck": true, "min_text_length_for_matching": 2, "max_text_length_for_matching": 5000, "enable_pattern_cache": true, "pattern_cache_size": 1000, "enable_batch_processing": true}, "parquet_data_source": {"_comments": "Parquet数据源配置 - 支持从两个文件夹读取同名parquet文件并合并", "enable": true, "folder_path_1": "F:/蕾奥工作/11.自研挂链/输入数据整理/result_date_part01", "folder_path_2": "F:/蕾奥工作/11.自研挂链/输入数据整理/result_date_part02", "file_extension": ".parquet", "merge_on_columns": ["lc_company_id", "company_name"], "batch_processing": {"enable_full_batch": true, "enable_random_test": true, "progress_report_interval": 10, "error_handling": {"continue_on_error": true, "max_errors": 50, "log_errors": true}}}, "keyword_table_columns": {"_comments": "关键词表格列定义", "label_columns": ["chain_name", "chain_level_1_name", "chain_level_2_name", "chain_level_3_name", "chain_level_4_name"], "filter_columns": ["industry_type", "source_scope"], "original_keyword_columns": ["like_keyword", "must_keyword", "unlike_keyword"], "converted_keyword_columns": ["converted_like_keyword", "converted_must_keyword", "converted_unlike_keyword"]}, "input_table_columns": {"_comments": "输入数据表格列定义", "identifier_columns": ["lc_company_id", "company_name"], "filter_columns": ["industry_l1_code"], "text_content_columns": ["company_profile", "stock_introduction", "stock_main_product", "yewu", "portray", "business_scope", "stock_business_scope", "bid_title", "allow_content", "app_brief", "land_project_name", "patent_title", "patent_abs", "software_full_name", "jing<PERSON>_yewu", "product_intro"]}, "matching_rules": {"_comments": "匹配规则配置", "industry_filter": {"enable": true, "default_value": "default", "separator": "、"}, "source_scope_filter": {"enable": true, "default_value": "default", "except_mappings": {"except_software": ["software_full_name"], "except_patent": ["patent_title", "patent_abs"]}, "separator": "、"}, "keyword_matching": {"like_keyword_default": "0", "must_keyword_default": "0", "unlike_keyword_default": "0", "match_tag_format": "_{type}_{original_keyword}"}}, "result_fields": {"_comments": "匹配结果字段配置 - 定义结果数据中的标识字段名称", "keyword_identifier": {"field_name": "keyword_index", "description": "关键词规则的唯一标识索引，用于追溯匹配到的具体关键词规则"}, "company_identifier": {"field_name": "company_id", "description": "企业的唯一标识ID，用于标识匹配成功的企业记录"}}}}